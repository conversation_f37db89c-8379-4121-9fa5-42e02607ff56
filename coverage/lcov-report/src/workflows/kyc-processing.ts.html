
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/workflows/kyc-processing.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/workflows</a> kyc-processing.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">12.19% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>5/41</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/28</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/4</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">13.15% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>5/38</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { DBOS } from "@dbos-inc/dbos-sdk";
import { KYCProfile } from '../types';
&nbsp;
export class KYCProcessing {
  
  // KYC Processing Steps
  @DBOS.step()
<span class="fstat-no" title="function not covered" >  static </span>async verifyIdentity(profile: KYCProfile): Promise&lt;{ verified: boolean; confidence: number }&gt; {
<span class="cstat-no" title="statement not covered" >    DBOS.logger.info(`Verifying identity for customer ${profile.customerId}`);</span>
&nbsp;
    // Simulate identity verification with random processing time (2-5 seconds)
    const processingTime = <span class="cstat-no" title="statement not covered" >Math.floor(Math.random() * 3000) + 2000;</span>
<span class="cstat-no" title="statement not covered" >    await DBOS.sleep(processingTime);</span>
&nbsp;
    // Mock verification logic
    const hasValidSSN = <span class="cstat-no" title="statement not covered" >profile.personalInfo.ssn &amp;&amp; profile.personalInfo.ssn.length &gt;= 9;</span> // Allow for various SSN formats
    const hasValidDOB = <span class="cstat-no" title="statement not covered" >profile.personalInfo.dateOfBirth &amp;&amp; new Date(profile.personalInfo.dateOfBirth) &lt; new Date();</span>
    const hasValidAddress = <span class="cstat-no" title="statement not covered" >profile.personalInfo.address &amp;&amp; profile.personalInfo.address.length &gt; 10;</span>
    const hasValidName = <span class="cstat-no" title="statement not covered" >profile.personalInfo.name &amp;&amp; profile.personalInfo.name.length &gt; 2;</span>
&nbsp;
    const confidence = <span class="cstat-no" title="statement not covered" >(hasValidSSN ? 0.3 : 0) +</span>
                      (hasValidDOB ? 0.25 : 0) +
                      (hasValidAddress ? 0.25 : 0) +
                      (hasValidName ? 0.2 : 0);
&nbsp;
    const verified = <span class="cstat-no" title="statement not covered" >confidence &gt;= 0.8;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    DBOS.logger.info(`Identity verification completed: ${verified ? 'PASSED' : 'FAILED'} (${confidence.toFixed(2)}) after ${processingTime}ms`);</span>
<span class="cstat-no" title="statement not covered" >    return { verified, confidence };</span>
  }
&nbsp;
  @DBOS.step()
<span class="fstat-no" title="function not covered" >  static </span>async performRiskAssessment(profile: KYCProfile): Promise&lt;number&gt; {
<span class="cstat-no" title="statement not covered" >    DBOS.logger.info(`Performing risk assessment for customer ${profile.customerId}`);</span>
    
    // Simulate risk assessment with random processing time (1-4 seconds)
    const processingTime = <span class="cstat-no" title="statement not covered" >Math.floor(Math.random() * 3000) + 1000;</span>
<span class="cstat-no" title="statement not covered" >    await DBOS.sleep(processingTime);</span>
    
    let riskScore = <span class="cstat-no" title="statement not covered" >0;</span>
    
    // Age-based risk (younger = higher risk)
    const age = <span class="cstat-no" title="statement not covered" >new Date().getFullYear() - new Date(profile.personalInfo.dateOfBirth).getFullYear();</span>
<span class="cstat-no" title="statement not covered" >    if (age &lt; 25) <span class="cstat-no" title="statement not covered" >riskScore += 20;</span></span>
    else <span class="cstat-no" title="statement not covered" ><span class="missing-if-branch" title="if path not taken" >I</span>if (age &lt; 35) <span class="cstat-no" title="statement not covered" >riskScore += 10;</span></span>
    
    // Address-based risk (simplified)
    const highRiskZipPrefixes = <span class="cstat-no" title="statement not covered" >['900', '800', '700'];</span> // Mock high-risk areas
    const zipCode = <span class="cstat-no" title="statement not covered" >profile.personalInfo.address.match(/\d{5}/)?.[0];</span>
<span class="cstat-no" title="statement not covered" >    <span class="missing-if-branch" title="if path not taken" >I</span>if (zipCode &amp;&amp; highRiskZipPrefixes.some(<span class="fstat-no" title="function not covered" >prefix </span>=&gt; <span class="cstat-no" title="statement not covered" >zipCode.startsWith(prefix))</span>) {</span>
<span class="cstat-no" title="statement not covered" >      riskScore += 30;</span>
    }
    
    // Random factor for demonstration
<span class="cstat-no" title="statement not covered" >    riskScore += Math.floor(Math.random() * 20);</span>
    
<span class="cstat-no" title="statement not covered" >    DBOS.logger.info(`Risk assessment completed: score ${riskScore} after ${processingTime}ms`);</span>
<span class="cstat-no" title="statement not covered" >    return Math.min(riskScore, 100);</span>
  }
&nbsp;
  @DBOS.step()
<span class="fstat-no" title="function not covered" >  static </span>async checkSanctionsList(profile: KYCProfile): Promise&lt;{ isListed: boolean; details?: string }&gt; {
<span class="cstat-no" title="statement not covered" >    DBOS.logger.info(`Checking sanctions list for customer ${profile.customerId}`);</span>
    
    // Simulate sanctions list check with random processing time (1-3 seconds)
    const processingTime = <span class="cstat-no" title="statement not covered" >Math.floor(Math.random() * 2000) + 1000;</span>
<span class="cstat-no" title="statement not covered" >    await DBOS.sleep(processingTime);</span>
    
    // Mock sanctions check - in production, this would query OFAC, UN, etc.
    const sanctionedNames = <span class="cstat-no" title="statement not covered" >['John Doe', 'Jane Smith'];</span> // Mock list
    const isListed = <span class="cstat-no" title="statement not covered" >sanctionedNames.includes(profile.personalInfo.name);</span>
    
    const result = <span class="cstat-no" title="statement not covered" >{</span>
      isListed,
      details: isListed ? 'Found match in OFAC sanctions list' : undefined
    };
    
<span class="cstat-no" title="statement not covered" >    DBOS.logger.info(`Sanctions check completed: ${isListed ? 'MATCH FOUND' : 'CLEAR'} after ${processingTime}ms`);</span>
<span class="cstat-no" title="statement not covered" >    return result;</span>
  }
}
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-14T03:52:14.949Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    